# 前端Agent页面接入规范文档

## 概述

本文档用于指导开发者将他们的agent页面接入到人际关系助手平台中，确保代码质量、风格一致性和功能完整性。

## 1. 环境要求

### Node.js版本要求
- **严格要求**: Node.js 16.14.0
- **验证命令**: `node --version` 应输出 `v16.14.0`
- **安装方法**: 
  ```bash
  # 使用 NVM 管理版本（推荐）
  nvm install 16.14.0
  nvm use 16.14.0
  ```

### npm/yarn版本要求
- **npm**: 使用Node.js 16.14.0自带的npm版本
- **registry**: 必须配置为美团内部registry
  ```bash
  npm config set registry http://r.npm.sankuai.com
  ```

### 必要的全局依赖
- **NVM**: Node版本管理工具（推荐）
- **Git**: 版本控制工具
- **Chrome/Safari**: 现代浏览器（支持backdrop-filter等CSS特性）

### 环境检查
项目提供了环境检查脚本，请在开发前运行：
```bash
chmod +x check-env.sh
./check-env.sh
```

## 2. 项目结构说明

### 必需的目录结构
您的仓库应包含以下结构，我们将手动移动对应文件到合适位置：

```
your-agent-repo/
├── pages/                    # 页面组件
│   └── YourAgent.vue        # 主页面组件
├── components/              # 可复用组件
│   ├── YourAgentDialog.vue  # 弹窗组件
│   └── YourAgentCard.vue    # 卡片组件
├── apis/                    # API接口文件
│   └── yourAgent.ts         # TypeScript接口定义
├── assets/                  # 静态资源
│   ├── icons/              # 图标文件
│   └── images/             # 图片文件
└── styles/                  # 样式文件
    └── yourAgent.scss       # 组件样式
```

### 文件命名规范
- **页面文件**: 使用PascalCase，如 `YourAgent.vue`
- **组件文件**: 使用PascalCase，如 `YourAgentDialog.vue`
- **API文件**: 使用camelCase，如 `yourAgent.ts`
- **样式文件**: 使用camelCase，如 `yourAgent.scss`

## 3. 代码规范

### ESLint配置规则
项目使用美团内部ESLint配置，您的代码必须通过以下规则检查：

```javascript
// 基于项目.eslintrc配置
{
  "extends": [
    "@cs/eslint-config/eslintrc.vue3.js",
    "@cs/eslint-config/eslintrc.typescript-vue3.js"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "off",
    "no-console": "off",
    "no-debugger": "error",
    "vue/multi-word-component-names": "off",
    "import/prefer-default-export": "off",
    "no-shadow": "off",
    "@typescript-eslint/no-shadow": ["error"],
    "consistent-return": "off",
    "prettier/prettier": "off"
  }
}
```

### Prettier配置
```json
{
  "singleQuote": true,
  "semi": true,
  "trailingComma": "all",
  "printWidth": 100,
  "tabWidth": 2,
  "bracketSpacing": true,
  "arrowParens": "always",
  "endOfLine": "auto"
}
```

### TypeScript配置
- 使用严格模式: `"strict": true`
- 支持装饰器: `"experimentalDecorators": true`
- 路径别名: `"@/*": ["src/*"]`
- 目标版本: `"target": "es5"`

## 4. 命名规范

### 文件命名规范
- **Vue组件**: PascalCase (如: `PersonDetailPopup.vue`)
- **TypeScript文件**: camelCase (如: `relationApi.ts`)
- **样式文件**: camelCase (如: `personDetail.scss`)
- **图片资源**: kebab-case (如: `user-avatar.png`)

### 组件命名规范
- **组件名**: 使用PascalCase，至少两个单词
- **Props**: 使用camelCase
- **Events**: 使用camelCase
- **Slots**: 使用kebab-case

### 变量和函数命名规范
- **变量**: camelCase (如: `userName`, `isLoading`)
- **常量**: UPPER_SNAKE_CASE (如: `API_BASE_URL`)
- **函数**: camelCase (如: `getUserInfo`, `handleClick`)
- **类**: PascalCase (如: `UserService`)

### CSS类名命名规范
- **BEM方法论**: `block__element--modifier`
- **组件前缀**: 使用 `cyber-` 前缀 (如: `cyber-card`, `cyber-btn-primary`)
- **状态类**: 使用 `is-` 前缀 (如: `is-active`, `is-loading`)

## 5. 样式规范

### SCSS/CSS编写规范
**严格禁止压缩式CSS**，必须保持良好的格式和可读性：

```scss
// ✅ 正确示例
.cyber-agent-card {
  background: var(--bg-glass);
  border: 2px solid var(--border-accent);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;

  .agent-title {
    color: var(--text-primary);
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 12px;
  }

  .agent-description {
    color: var(--text-secondary);
    font-size: 16px;
    line-height: 1.5;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-strong);
  }
}

// ❌ 错误示例 - 压缩式CSS
.cyber-agent-card{background:var(--bg-glass);border:2px solid var(--border-accent);border-radius:16px;padding:20px;}
```

### 样式文件组织方式
```scss
// 1. 导入设计系统
@import '@/styles/design-system.scss';

// 2. 组件变量
$agent-card-width: 300px;
$agent-card-height: 200px;

// 3. 主要样式
.cyber-agent-container {
  // 容器样式
}

// 4. 子组件样式
.cyber-agent-card {
  // 卡片样式
  
  .agent-header {
    // 头部样式
  }
  
  .agent-content {
    // 内容样式
  }
}

// 5. 响应式样式
@media (max-width: 768px) {
  .cyber-agent-card {
    padding: 16px;
  }
}
```

## 6. 组件开发规范

### 组件结构要求
```vue
<template>
  <div class="cyber-your-agent">
    <!-- 组件内容 -->
  </div>
</template>

<script setup lang="ts">
// 1. 导入依赖
import { ref, computed, onMounted } from 'vue';
import { showToast } from 'vant';

// 2. 类型定义
interface IProps {
  userId: string;
  agentConfig?: IAgentConfig;
}

interface IAgentConfig {
  name: string;
  description: string;
}

// 3. Props定义
const props = withDefaults(defineProps<IProps>(), {
  agentConfig: () => ({ name: '', description: '' }),
});

// 4. Emits定义
const emit = defineEmits<{
  close: [];
  success: [data: any];
}>();

// 5. 响应式数据
const isLoading = ref(false);
const agentData = ref<IAgentConfig | null>(null);

// 6. 计算属性
const displayName = computed(() => {
  return agentData.value?.name || '未知Agent';
});

// 7. 方法定义
const handleSubmit = async () => {
  try {
    isLoading.value = true;
    // 处理逻辑
    emit('success', agentData.value);
  } catch (error) {
    showToast('操作失败');
  } finally {
    isLoading.value = false;
  }
};

// 8. 生命周期
onMounted(() => {
  // 初始化逻辑
});
</script>

<style lang="scss" scoped>
// 组件样式
</style>
```

### Props定义规范
- 使用TypeScript接口定义Props类型
- 提供默认值使用 `withDefaults`
- 必需属性不提供默认值
- 可选属性提供合理默认值

### 组件命名要求
- 组件名必须是多个单词组成
- 使用PascalCase命名
- 文件名与组件名保持一致

### 生命周期使用规范
- 优先使用Composition API
- 按照执行顺序组织生命周期钩子
- 在 `onBeforeUnmount` 中清理资源

## 7. 检查清单 (Checklist)

### 环境检查 ✅
- [ ] Node.js版本为16.14.0
- [ ] npm registry配置正确
- [ ] 运行环境检查脚本通过
- [ ] 依赖安装成功

### 代码质量 ✅
- [ ] ESLint检查通过，无错误和警告
- [ ] TypeScript类型检查通过
- [ ] 代码格式符合Prettier规范

### 文件结构 ✅
- [ ] 目录结构符合规范
- [ ] 文件命名符合规范
- [ ] 组件文件包含必要的类型定义
- [ ] API文件使用TypeScript编写

### 样式规范 ✅
- [ ] 使用设计系统中的CSS变量
- [ ] 样式代码格式良好，非压缩式
- [ ] 使用cyber-前缀的类名
- [ ] 响应式设计适配移动端

### 组件规范 ✅
- [ ] 组件使用Composition API
- [ ] Props和Emits有完整类型定义
- [ ] 组件名符合多单词要求
- [ ] 生命周期使用规范

### 功能测试 ✅
- [ ] 组件功能正常运行
- [ ] 错误处理完善
- [ ] 用户交互体验良好
- [ ] 移动端适配正常

**完成所有检查项后，您的Agent页面就可以成功接入平台了！**
