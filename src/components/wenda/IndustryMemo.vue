<template>
  <div class="industry-memo-container">
    <div class="memo-title">行业备忘录</div>
    <div class="memo-content">
      <!-- 内容区域暂时留空 -->
    </div>
    <div class="memo-add-btn" @click="handleAddClick">
      <span class="add-icon">+</span>
    </div>
  </div>
</template>

<script setup lang="ts">
// Emits定义
const emit = defineEmits<{
  addIndustryMemo: [];
}>();

// 处理添加按钮点击事件
const handleAddClick = () => {
  console.log('行业备忘录添加按钮被点击');
  emit('addIndustryMemo');
};
</script>

<style lang="scss" scoped>
.industry-memo-container {
  height: 80px;
  width: 100%;
  background: #e3f2fd; // 浅蓝色底
  border-radius: 12px;
  display: flex;
  align-items: center;
  padding: 16px;
  box-sizing: border-box;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .memo-title {
    color: #2196f3; // 蓝色标题
    font-size: 26px;
    font-weight: 600;
    white-space: nowrap;
    margin-right: 16px;
  }

  .memo-content {
    flex: 1;
    height: 100%;
    // 内容区域暂时留空
  }

  .memo-add-btn {
    width: 40px;
    height: 40px;
    background: #2196f3;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;

    .add-icon {
      color: white;
      font-size: 24px;
      font-weight: bold;
    }
  }
}
</style>
