<template>
  <div class="dialog-overlay">
    <div class="dialog-container">
      <div class="dialog-header">
        <div class="dialog-title">公司知识库</div>
        <div class="dialog-close" @click="$emit('close')">
          <DeleteIcon :size="24" color="var(--primary-color)" />
        </div>
      </div>
      <div class="dialog-content">
        <!-- 知识库列表区域 -->
        <div class="knowledge-section">
          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-state">
            <div class="loading-text">加载中...</div>
          </div>
          
          <!-- 知识库条目列表 -->
          <div v-else class="knowledge-list">
            <div
              v-for="item in currentPageItems"
              :key="item.id"
              class="knowledge-item"
            >
              <div class="knowledge-term">{{ item.term }}</div>
              <div class="knowledge-definition">{{ item.definition }}</div>
            </div>
            
            <!-- 空状态 -->
            <div v-if="knowledgeItems.length === 0" class="empty-state">
              <div class="empty-text">暂无知识库内容</div>
            </div>
          </div>
          
          <!-- 分页控制 -->
          <div v-if="totalPages > 1" class="pagination-controls">
            <button
              class="pagination-btn"
              :disabled="currentPage === 1"
              @click="goToPage(currentPage - 1)"
            >
              上一页
            </button>
            <span class="pagination-info">
              {{ currentPage }} / {{ totalPages }}
            </span>
            <button
              class="pagination-btn"
              :disabled="currentPage === totalPages"
              @click="goToPage(currentPage + 1)"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue';
import { getKnowledgeList, type IKnowledgeItem } from '@/apis/knowledge';
import DeleteIcon from '@/assets/icons/DeleteIcon.vue';

// Emits定义
const emit = defineEmits<{
  close: [];
}>();

// 响应式数据
const knowledgeItems = ref<IKnowledgeItem[]>([]);
const isLoading = ref(false);
const currentPage = ref(1);
const pageSize = 10; // 每页显示10个条目

// 计算属性
const totalPages = computed(() => {
  return Math.ceil(knowledgeItems.value.length / pageSize);
});

const currentPageItems = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  return knowledgeItems.value.slice(startIndex, endIndex);
});

// 获取知识库数据
const fetchKnowledgeItems = async () => {
  try {
    isLoading.value = true;
    console.log('📤 [KnowledgeDialog] 开始获取知识库数据');

    const response = await getKnowledgeList({
      limit: 100,
      offset: 0,
    });

    if (response.result === 'success' && response.knowledge_items) {
      knowledgeItems.value = response.knowledge_items;
      console.log('✅ [KnowledgeDialog] 知识库数据获取成功:', knowledgeItems.value.length, '条');
    } else {
      console.warn('⚠️ [KnowledgeDialog] 知识库数据获取失败或无数据');
      knowledgeItems.value = [];
    }
  } catch (error) {
    console.error('❌ [KnowledgeDialog] 获取知识库数据失败:', error);
    knowledgeItems.value = [];
  } finally {
    isLoading.value = false;
  }
};

// 分页方法
const goToPage = async (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
    // 切换页面后，将知识库列表滚动到顶部
    await nextTick();
    const knowledgeList = document.querySelector('.knowledge-list');
    if (knowledgeList) {
      knowledgeList.scrollTop = 0;
    }
  }
};

// 组件挂载时获取数据
onMounted(() => {
  void fetchKnowledgeItems();
});
</script>

<style lang="scss" scoped>
// 对话框样式
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

.dialog-container {
  background: var(--bg-glass-popup);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 650px;
  max-height: calc(90vh + 100px);
  color: white;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 18px;
  position: relative;

  .dialog-title {
    color: rgba(00, 00, 00, 0.9);
    font-size: 40px; // 增加8px (原来32px)
    font-weight: 600;
  }

  .dialog-close {
    background: transparent;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
  }
}

.dialog-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.knowledge-section {
  border: 2px solid var(--border-accent);
  border-radius: 16px;
  padding: 24px;
  background: var(--primary-color-light);
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;

  .loading-text {
    color: var(--accent-color);
    font-size: 32px;
    font-weight: 600;
  }
}

.knowledge-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 400px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--bg-glass-light);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
}

.knowledge-item {
  background: var(--bg-glass-light);
  border: 2px solid var(--border-accent);
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;

  .knowledge-term {
    color: #000000;
    font-size: 30px; // 增加8px
    font-weight: 600;
    margin-bottom: 8px;
  }

  .knowledge-definition {
    color: #000000;
    font-size: 26px; // 增加8px
    line-height: 1.5;
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;

  .empty-text {
    color: var(--text-tertiary);
    font-size: 28px;
    font-style: italic;
  }
}

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-accent);

  .pagination-btn {
    background: var(--bg-glass-light);
    border: 2px solid var(--border-accent);
    border-radius: 12px;
    padding: 12px 20px;
    color: #000000;
    font-size: 26px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .pagination-info {
    color: #000000;
    font-size: 26px;
    font-weight: 600;
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
