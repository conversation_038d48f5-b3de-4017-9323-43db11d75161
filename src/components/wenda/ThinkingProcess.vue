<template>
  <div class="thinking-process">
    <!-- 思考过程标题 -->
    <div class="thinking-header">
      <div class="thinking-icon">🤔</div>
      <div class="thinking-title">AI思考过程</div>
    </div>

    <!-- 思考内容列表 -->
    <div class="thinking-content">
      <div
        v-for="(item, index) in thinkingItems"
        :key="index"
        class="thinking-item"
        :class="{ 'fade-in': item.visible }"
      >
        <div class="thinking-item-icon">
          <div v-if="item.type === 'status'" class="status-dot" :class="item.step"></div>
          <div v-else-if="item.type === 'question'" class="question-icon">❓</div>
          <div v-else-if="item.type === 'log'" class="log-icon">📝</div>
          <div v-else-if="item.type === 'search_questions'" class="search-icon">🔍</div>
          <div v-else-if="item.type === 'search_result'" class="result-icon">📋</div>
        </div>
        <div class="thinking-item-content">
          <div v-if="item.type === 'status'" class="status-message">
            {{ item.message }}
          </div>
          <div v-else-if="item.type === 'question'" class="question-content">
            <span class="question-label">用户询问：</span>
            <span class="question-text">{{ item.question }}</span>
          </div>
          <div v-else-if="item.type === 'log'" class="log-content">
            {{ item.message }}
          </div>
          <div v-else-if="item.type === 'search_questions'" class="search-questions-content">
            <div class="search-questions-label">AI分析如下角度搜索：</div>
            <ul class="questions-list">
              <li v-for="(question, qIndex) in item.questions" :key="qIndex" class="question-item">
                {{ question }}
              </li>
            </ul>
          </div>
          <div v-else-if="item.type === 'search_result'" class="search-result-content">
            <div class="search-result-title">搜索结果：</div>
            <div v-for="(result, rIndex) in item.results" :key="rIndex" class="result-item">
              <a :href="result.link" target="_blank" class="result-link">
                {{ result.title }}
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-indicator">
      <div class="loading-dots">
        <span class="dot dot1">.</span>
        <span class="dot dot2">.</span>
        <span class="dot dot3">.</span>
      </div>
      <span class="loading-text">思考中...</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue';

// 思考项目接口
interface IThinkingItem {
  type: 'status' | 'question' | 'log' | 'search_questions' | 'search_result';
  message?: string;
  step?: 'start' | 'processing' | 'complete';
  question?: string;
  questions?: string[];
  results?: Array<{
    title: string;
    link: string;
  }>;
  visible: boolean;
}

// 响应式数据
const thinkingItems = ref<IThinkingItem[]>([]);
const isLoading = ref(false);

// 添加状态消息
const addStatus = async (message: string, step: 'start' | 'processing' | 'complete') => {
  const item: IThinkingItem = {
    type: 'status',
    message,
    step,
    visible: false,
  };
  thinkingItems.value.push(item);
  
  await nextTick();
  item.visible = true;
  
  // 如果是开始状态，显示加载指示器
  if (step === 'start') {
    isLoading.value = true;
  } else if (step === 'complete') {
    isLoading.value = false;
  }
};

// 添加问题
const addQuestion = async (question: string) => {
  const item: IThinkingItem = {
    type: 'question',
    question,
    visible: false,
  };
  thinkingItems.value.push(item);
  
  await nextTick();
  item.visible = true;
};

// 添加日志
const addLog = async (message: string) => {
  const item: IThinkingItem = {
    type: 'log',
    message,
    visible: false,
  };
  thinkingItems.value.push(item);
  
  await nextTick();
  item.visible = true;
};

// 添加搜索问题
const addSearchQuestions = async (questions: string[]) => {
  const item: IThinkingItem = {
    type: 'search_questions',
    questions,
    visible: false,
  };
  thinkingItems.value.push(item);
  
  await nextTick();
  item.visible = true;
};

// 添加搜索结果
const addSearchResult = async (results: Array<{ title: string; link: string }>) => {
  const item: IThinkingItem = {
    type: 'search_result',
    results,
    visible: false,
  };
  thinkingItems.value.push(item);
  
  await nextTick();
  item.visible = true;
};

// 清空思考过程
const clear = () => {
  thinkingItems.value = [];
  isLoading.value = false;
};

// 完成思考过程
const complete = () => {
  isLoading.value = false;
};

// 暴露方法给父组件
defineExpose({
  addStatus,
  addQuestion,
  addLog,
  addSearchQuestions,
  addSearchResult,
  clear,
  complete,
});
</script>

<style lang="scss" scoped>
.thinking-process {
  width: 100%;
  background: rgba(30, 58, 138, 0.1);
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 16px;
  padding: 20px;
  margin: 16px 0;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);

  .thinking-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2);

    .thinking-icon {
      font-size: 24px;
    }

    .thinking-title {
      font-size: 20px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.9);
    }
  }

  .thinking-content {
    .thinking-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      margin-bottom: 16px;
      opacity: 0;
      transform: translateY(10px);
      transition: all 0.3s ease;

      &.fade-in {
        opacity: 1;
        transform: translateY(0);
      }

      .thinking-item-icon {
        flex-shrink: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;

        .status-dot {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          
          &.start {
            background: #00bcd4;
            animation: pulse 1.5s infinite;
          }
          
          &.processing {
            background: #ffa726;
            animation: pulse 1.5s infinite;
          }
          
          &.complete {
            background: #4caf50;
          }
        }

        .question-icon,
        .log-icon,
        .search-icon,
        .result-icon {
          font-size: 16px;
        }
      }

      .thinking-item-content {
        flex: 1;
        font-size: 16px;
        line-height: 1.5;
        color: rgba(255, 255, 255, 0.8);

        .status-message {
          color: rgba(255, 255, 255, 0.9);
          font-weight: 500;
        }

        .question-content {
          .question-label {
            color: #00bcd4;
            font-weight: 600;
          }
          
          .question-text {
            color: rgba(255, 255, 255, 0.9);
          }
        }

        .log-content {
          color: rgba(255, 255, 255, 0.8);
        }

        .search-questions-content {
          .search-questions-label {
            color: #00bcd4;
            font-weight: 600;
            margin-bottom: 8px;
          }

          .questions-list {
            margin: 0;
            padding-left: 20px;

            .question-item {
              margin-bottom: 4px;
              color: rgba(255, 255, 255, 0.8);
            }
          }
        }

        .search-result-content {
          .search-result-title {
            color: #00bcd4;
            font-weight: 600;
            margin-bottom: 8px;
          }

          .result-item {
            margin-bottom: 4px;

            .result-link {
              color: rgba(255, 255, 255, 0.8);
              text-decoration: none;
              
              &:hover {
                color: #00ffff;
                text-decoration: underline;
              }
            }
          }
        }
      }
    }
  }

  .loading-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;

    .loading-dots {
      display: flex;
      gap: 4px;

      .dot {
        display: inline-block;
        opacity: 0.3;
        animation: dotPulse 0.8s infinite;
        color: #00ffff;
        font-size: 20px;
        font-weight: bold;
      }

      .dot1 {
        animation-delay: 0s;
      }

      .dot2 {
        animation-delay: 0.25s;
      }

      .dot3 {
        animation-delay: 0.5s;
      }
    }

    .loading-text {
      color: rgba(255, 255, 255, 0.7);
      font-size: 16px;
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes dotPulse {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
}
</style>
