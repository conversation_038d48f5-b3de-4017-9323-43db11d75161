<template>
  <div class="barrage-container">
    <!-- 弹幕列表 -->
    <TransitionGroup name="barrage" tag="div" class="barrage-list">
      <div
        v-for="item in visibleBarrages"
        :key="item.id"
        class="barrage-item"
        :class="[item.type]"
        @animationend="handleAnimationEnd(item.id)"
      >
        <div class="barrage-content">
          <div class="barrage-icon">
            <svg
              v-if="item.type === 'memo'"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14,2 14,8 20,8"></polyline>
              <line x1="16" y1="13" x2="8" y2="13"></line>
              <line x1="16" y1="17" x2="8" y2="17"></line>
              <polyline points="10,9 9,9 8,9"></polyline>
            </svg>
            <svg v-else viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polygon points="13,2 3,14 12,14 11,22 21,10 12,10 13,2"></polygon>
            </svg>
          </div>
          <div class="barrage-text">{{ item.content }}</div>
        </div>
      </div>
    </TransitionGroup>

    <!-- 闪烁特效容器 -->
    <div class="sparkle-container">
      <div
        v-for="sparkle in sparkles"
        :key="sparkle.id"
        class="sparkle"
        :style="{
          left: sparkle.x + 'px',
          top: sparkle.y + 'px',
          animationDelay: sparkle.delay + 's',
        }"
      >
        ✨
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

// 弹幕数据接口
interface IBarrageItem {
  id: string;
  type: 'memo' | 'knowledge';
  content: string;
  timestamp: number;
}

// 闪烁特效接口
interface ISparkle {
  id: string;
  x: number;
  y: number;
  delay: number;
}

// 响应式数据
const barrages = ref<IBarrageItem[]>([]);
const sparkles = ref<ISparkle[]>([]);
const maxVisible = 1; // 修改为同时只显示1条弹幕

// 计算属性：可见的弹幕列表
const visibleBarrages = computed(() => {
  return barrages.value.slice(-maxVisible);
});

// 生成闪烁特效
const createSparkles = () => {
  const newSparkles: ISparkle[] = [];
  const sparkleCount = 25; // 增加特效数量，让轨迹更密集

  // 获取app容器的宽度和位置
  const appElement = document.querySelector('.v-chat-container') as HTMLElement;
  const appRect = appElement
    ? appElement.getBoundingClientRect()
    : { width: window.innerWidth, left: 0 };

  // memo-btn的精确位置（右上角，基于app容器）
  const memoBtnX = appRect.left + appRect.width - 100; // 基于app容器的绝对位置
  const memoBtnY = 50; // memo-btn距离顶部约50px

  // 弹幕路径：从页面底部中央到右上角memo-btn
  const startY = window.innerHeight - 200; // 从底部开始
  const endY = memoBtnY;
  const startX = appRect.left + appRect.width / 2; // 从app容器中间开始
  const endX = memoBtnX;

  // 分阶段生成特效，精确跟随弹幕轨迹
  for (let i = 0; i < sparkleCount; i++) {
    const progress = i / (sparkleCount - 1);

    // 计算弹幕路径上的精确位置，减少随机偏移
    const x = startX + (endX - startX) * progress + (Math.random() - 0.5) * 30; // 更小的随机偏移
    const y = startY + (endY - startY) * progress + (Math.random() - 0.5) * 20; // 更小的随机偏移

    // 计算弹幕到达该位置的时间（弹幕动画15%开始移动，85%到达终点）
    const barrageStartTime = 0.15; // 弹幕15%时开始移动
    const barrageEndTime = 0.85; // 弹幕85%时到达终点
    const barragePassTime = barrageStartTime + (barrageEndTime - barrageStartTime) * progress;

    newSparkles.push({
      id: `sparkle_${Date.now()}_${i}`,
      x,
      y,
      delay: barragePassTime * 4, // 弹幕经过该位置的时间（4秒总时长）
    });
  }

  sparkles.value = newSparkles;

  // 6秒后清除特效（比弹幕动画长一些，让特效有时间变暗）
  setTimeout(() => {
    sparkles.value = [];
  }, 6000);
};

// 显示弹幕的方法
const showBarrage = (type: 'memo' | 'knowledge', content: string) => {
  const newBarrage: IBarrageItem = {
    id: `barrage_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    type,
    content,
    timestamp: Date.now(),
  };

  // 获取app容器的宽度，设置CSS自定义属性
  const appElement = document.querySelector('.v-chat-container') as HTMLElement;
  const appRect = appElement
    ? appElement.getBoundingClientRect()
    : { width: window.innerWidth, left: 0 };
  const targetLeft = appRect.left + appRect.width - 150;

  // 设置CSS自定义属性
  document.documentElement.style.setProperty('--barrage-target-left', `${targetLeft}px`);
  document.documentElement.style.setProperty(
    '--barrage-start-left',
    `${appRect.left + appRect.width / 2}px`,
  );

  barrages.value.push(newBarrage);

  // 生成闪烁特效
  createSparkles();

  // 自动移除弹幕（4秒后，确保动画完成）
  setTimeout(() => {
    removeBarrage(newBarrage.id);
  }, 4000);
};

// 移除弹幕
const removeBarrage = (id: string) => {
  const index = barrages.value.findIndex((item) => item.id === id);
  if (index !== -1) {
    barrages.value.splice(index, 1);
  }
};

// 处理动画结束事件
const handleAnimationEnd = (id: string) => {
  // 动画结束后可以做一些清理工作
  console.log('弹幕动画结束:', id);
};

// 暴露方法给父组件
defineExpose({
  showBarrage,
});
</script>

<style lang="scss" scoped>
.barrage-container {
  position: fixed; // 改为固定定位，相对于视口
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999; // 确保在所有聊天组件之上
  pointer-events: none; // 不阻挡用户交互
  overflow: visible; // 允许弹幕超出边界显示
}

.barrage-list {
  position: relative;
  width: 100%;
  height: 100%;
}

.barrage-item {
  position: absolute;
  // 初始位置：app容器底部中央
  left: var(--barrage-start-left, 50%);
  top: calc(100vh - 200px); // 从底部开始
  transform: translate(-50%, 0);
  border-radius: 16px; // 使用chatItem的圆角
  backdrop-filter: blur(20px); // 保持模糊背景
  border: 2px solid;
  box-shadow: var(--shadow-strong), var(--shadow-accent); // 使用设计系统阴影
  animation: flyToMemo 4s ease-in-out forwards; // 飞向memo-btn的动画
  max-width: 700px; // 限制最大宽度
  padding: 16px 20px; // 添加移动端的padding设置
  pointer-events: auto; // 允许点击交互
  cursor: pointer;
  transition: transform 0.3s ease;

  // 备忘录类型样式 - 参考chatItem的玻璃态风格
  &.memo {
    background: var(--bg-glass-light); // 使用设计系统背景
    border-color: var(--primary-color); // 使用主色调边框
    color: var(--text-primary); // 使用主文字颜色
    box-shadow:
      var(--shadow-strong),
      0 0 20px var(--primary-color-strong); // 添加发光效果
  }

  // 懂量类型样式 - 使用绿色系
  &.knowledge {
    background: var(--bg-glass-light); // 使用设计系统背景
    border-color: var(--accent-color); // 使用强调色边框
    color: var(--text-primary); // 使用主文字颜色
    box-shadow:
      var(--shadow-strong),
      0 0 20px var(--accent-color-medium); // 添加发光效果
  }


}

.barrage-content {
  display: flex;
  align-items: center;
  gap: 12px; // 使用移动端的较小间距
  white-space: nowrap;
}

.barrage-icon {
  width: 24px; // 使用移动端的图标大小
  height: 24px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    width: 100%;
    height: 100%;
  }
}

.barrage-text {
  font-size: var(--font-size-2xl);
  font-weight: 500; // 增加字重
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5); // 增强文字阴影
  line-height: 1.4;
}

// 闪烁特效容器
.sparkle-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10000;
}

// 闪烁特效样式
.sparkle {
  position: absolute;
  font-size: 16px;
  color: #00ffff;
  animation: sparkleTrail 3s ease-out forwards;
  pointer-events: none;
  text-shadow:
    0 0 6px #00ffff,
    0 0 12px #00ffff,
    0 0 18px #00ffff;
  filter: brightness(1.3);
  opacity: 0;
}

// 从页面底部飞向memo-btn的动画
@keyframes flyToMemo {
  0% {
    opacity: 0;
    transform: translate(-50%, 0) scale(0.8);
    top: calc(100vh - 200px); // 从底部开始
    left: var(--barrage-start-left, 50%);
  }

  15% {
    opacity: 1;
    transform: translate(-50%, 0) scale(1);
    top: calc(100vh - 200px);
    left: var(--barrage-start-left, 50%);
  }

  85% {
    opacity: 1;
    transform: translate(-50%, 0) scale(0.8);
    top: 50px; // 移动到顶部memo-btn位置
    left: var(--barrage-target-left, calc(100% - 100px)); // 使用动态计算的位置
  }

  100% {
    opacity: 0;
    transform: translate(-50%, 0) scale(0.3);
    top: 50px;
    left: var(--barrage-target-left, calc(100% - 100px));
  }
}

// 闪烁特效动画 - 弹幕经过时出现，然后逐渐变暗
@keyframes sparkleTrail {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
    filter: brightness(1.3) saturate(1);
  }

  5% {
    opacity: 1;
    transform: scale(1.3) rotate(45deg);
    filter: brightness(1.5) saturate(1.2);
  }

  15% {
    opacity: 1;
    transform: scale(1) rotate(90deg);
    filter: brightness(1.3) saturate(1);
  }

  40% {
    opacity: 0.8;
    transform: scale(0.9) rotate(180deg);
    filter: brightness(1) saturate(0.8);
  }

  70% {
    opacity: 0.4;
    transform: scale(0.7) rotate(270deg);
    filter: brightness(0.6) saturate(0.5);
  }

  100% {
    opacity: 0;
    transform: scale(0.3) rotate(335deg);
    filter: brightness(0.3) saturate(0.2);
  }
}
</style>
