<template>
  <div
    class="input-bar"
    :class="{
      'voice-active': isRecording,
    }"
  >
    <!-- 统一的输入模式 - 始终显示输入框 -->
    <div class="keyboard-wrapper">
      <div class="input-box">
        <div class="input-content-wrapper">
          <van-field
            ref="textInputRef"
            v-model="textMessage"
            class="input-content"
            rows="1"
            autosize
            autocomplete="off"
            inputmode="text"
            type="textarea"
            :placeholder="isRecording ? '我在听，请说...' : '可以问我任何问题'"
            @keydown.enter.prevent="handleSend"
            @compositionstart="handleComposition"
            @compositionupdate="handleComposition"
            @compositionend="handleComposition"
            @input="handleInput"
          />
          <!-- 麦克风按钮在输入框内部右侧 -->
          <div class="voice-toggle-inner" :class="{ breathing: isRecording }" @click="handleVoiceButtonClick">
            <MicrophoneIcon :size="16" />
          </div>
        </div>
      </div>

      <!-- 发送按钮 -->
      <button class="send-btn" title="发送" @click="handleSend">
        <SendIcon class="send-icon" width="32px" height="32px" />
      </button>

      <!-- 用户头像 -->
      <div class="user-avatar-container">
        <div class="user-avatar" :style="{ backgroundColor: displayAvatarUrl ? 'transparent' : userAvatarColor }">
          <img v-if="displayAvatarUrl" :src="displayAvatarUrl" alt="用户头像" />
          <span v-else>{{ userAvatarLetter }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick, computed } from 'vue';
import { showToast, Field as vanField } from 'vant';
import { getStreamAsr } from '@/apis/chat';

import Recorder from 'recorder-realtime';
import { generateRandomString } from '@/utils';
import { debounce } from 'lodash-es';
import { useRoute } from 'vue-router';
import MicrophoneIcon from '@/assets/icons/MicrophoneIcon.vue';
import SendIcon from '@/assets/icons/SendIcon.vue';
import { useUserStore } from '@/stores/user';
import { getUserProfile } from '@/apis/relation';
import { getAvatarUrl } from '@/utils/avatarUtils';
import { getUserInfo } from '@/apis/common';

// Props
const props = defineProps<{
  isLoading?: boolean;
}>();

// Emits
const emit = defineEmits<{
  (e: 'voice-send', message: string): void;
  (e: 'send', message: string): void;
  (e: 'update:modelValue', value: string): void;
  (e: 'stop'): void;
  (e: 'recording-status', isRecording: boolean): void;
}>();

const route = useRoute();
const userStore = useUserStore();

// 用户头像相关
const userRealAvatar = ref<string>('');

const userAvatarColor = computed(() => {
  const userId = userStore.userInfo?.login || 'user';
  // 简单的颜色生成逻辑
  const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
  const index = userId.length % colors.length;
  return colors[index];
});

const userAvatarLetter = computed(() => {
  const userId = userStore.userInfo?.login || 'U';
  // 返回用户ID的第一个字符作为头像字母
  return userId.charAt(0).toUpperCase();
});

// 计算最终显示的头像URL
const displayAvatarUrl = computed(() => {
  // 优先使用userStore中的头像（实时更新）
  if (userStore.userAvatar) {
    return getAvatarUrl(userStore.userAvatar);
  }
  // 如果userStore中没有头像，使用本地加载的头像
  if (userRealAvatar.value) {
    return getAvatarUrl(userRealAvatar.value);
  }
  // 如果没有头像，返回 null，这样会显示字母头像
  return null;
});

// 获取用户真实头像
const loadUserAvatar = async () => {
  try {
    let userId = userStore.userInfo?.login;

    // 如果 userStore 中没有用户信息，直接调用 getUserInfo API
    if (!userId) {
      console.log('ℹ️ [inputBar.vue] userStore中没有用户信息，直接获取用户信息');
      try {
        const userInfo = await getUserInfo();
        if (userInfo && userInfo.login) {
          userId = userInfo.login;
          userStore.userInfo = userInfo; // 设置到 userStore 中
          console.log('✅ [inputBar.vue] 用户信息获取成功:', userId);
        } else {
          console.warn('⚠️ [inputBar.vue] 获取的用户信息格式异常');
          return;
        }
      } catch (error) {
        console.warn('⚠️ [inputBar.vue] 获取用户信息失败:', error);
        return;
      }
    }

    const userProfileResponse = await getUserProfile({
      user_id: userId,
    });

    if (userProfileResponse.result === 'success' && userProfileResponse.person?.avatar) {
      userRealAvatar.value = userProfileResponse.person.avatar;
      // 同时更新userStore中的头像
      userStore.updateUserAvatar(userProfileResponse.person.avatar);
      console.log('✅ [inputBar.vue] 用户头像加载成功:', userRealAvatar.value);
    } else {
      console.log('ℹ️ [inputBar.vue] 用户没有设置头像，使用字母头像');
    }
  } catch (error) {
    console.warn('⚠️ [inputBar.vue] 获取用户头像失败:', error);
  }
};

// 录音相关变量
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let timerId: ReturnType<typeof setTimeout> | null = null;
// 保存媒体流引用，用于释放麦克风资源
let mediaStream: MediaStream | null = null;

// 响应式数据
const micPermission = ref(false); // 麦克风权限
const sessionId = ref(''); // 语音转文字sessionId
const audioBufferIndex = ref(0); // 语音转文字流序列号
const lastBuffer = ref(); // 语音转文字最后一条流
const voiceMessage = ref(''); // 发送的对话文字
const isRecording = ref(false); // 是否录音输入

// 新增状态
const textMessage = ref(''); // 文字输入内容
const isOnComposition = ref(false); // 是否在输入法组合状态
const textInputRef = ref(); // 输入框引用
const lastVoiceText = ref(''); // 上次语音识别的文字，用于增量更新

// 设置麦克风权限（仅在需要时请求）
async function setMicPermission() {
  try {
    // 保存媒体流引用，用于后续释放资源
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    micPermission.value = false;
    showToast('请授权麦克风权限~');
  }
}

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    // 如果浏览器不支持录音功能，给用户提示
    showToast('录音失败，浏览器不支持录音功能');
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  // 当录音开始时的回调
  recorder.onstart = () => {};

  // 处理录音错误的回调
  recorder.onstreamerror = () => {
    // 显示录音错误消息并停止录音
    showToast('录音失败');
    cancelRecording();
  };

  // 当数据可用时的回调
  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      const streamData = await getStreamAsr({
        sessionId: sessionId.value,
        format: 'pcm',
        sampleRate: 16000,
        index: audioBufferIndex.value,
        data: data.buffer,
      });
      // 修复：检查 full_text 而不是 text，并且确保 full_text 不为空且与当前值不同
      if (
        streamData.data.full_text &&
        streamData.data.full_text.trim() !== '' &&
        streamData.data.full_text !== lastVoiceText.value
      ) {
        // 计算新增的文字部分
        const newText = streamData.data.full_text;
        const previousText = lastVoiceText.value;

        // 如果新文字包含之前的文字，只插入新增部分
        let textToInsert = newText;
        if (previousText && newText.startsWith(previousText)) {
          textToInsert = newText.slice(previousText.length);
        }

        // 在光标位置插入新文字
        if (textToInsert) {
          insertTextAtCursor(textToInsert);
        }

        lastVoiceText.value = newText;
        voiceMessage.value = newText;
        await autoSendTimeout();
      }
    }
  };
};

// 开始录音
async function startRecording() {
  if (route.meta.pageCase) {
    route.meta.pageCase('moduleClick', 'b_smartassistant_z15ks0wo_mc');
  }
  if (props.isLoading) {
    return;
  }
  if (isRecording.value) {
    await stopRecording();
    return;
  }

  // 如果没有麦克风权限，先请求权限
  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    // 通知父组件录音状态变化
    emit('recording-status', true);
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    // 重置语音识别状态
    lastVoiceText.value = '';
    voiceMessage.value = '';
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopRecording();
    }, 1000 * 60);
  }
}

// 两秒不说话自动发送
const autoSendTimeout = debounce(async () => {
  await stopRecording();
}, 2000);

// 释放麦克风资源
function releaseMicrophoneResources() {
  if (mediaStream) {
    // 停止所有媒体轨道，释放麦克风资源
    mediaStream.getTracks().forEach((track) => {
      track.stop();
      console.log('🎤 [inputBar] 释放麦克风轨道:', track.kind);
    });
    mediaStream = null;
    micPermission.value = false;
    console.log('✅ [inputBar] 麦克风资源已释放');
  }
}

// 取消录音
function cancelRecording() {
  if (recorder) {
    recorder.stop();
  }
  isRecording.value = false;
  // 通知父组件录音状态变化
  emit('recording-status', false);
  voiceMessage.value = '';
  // 释放麦克风资源
  releaseMicrophoneResources();
}

// 结束录音
async function stopRecording() {
  if (!isRecording.value) return;
  isRecording.value = false;
  // 通知父组件录音状态变化
  emit('recording-status', false);
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null; // 重置定时器 ID
  }
  if (recorder) {
    recorder.stop();
  }

  // 释放麦克风资源
  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });
  if (voiceMessage.value) {
    // 语音识别完成，文字已经通过 insertTextAtCursor 插入到光标位置
    // 不需要再次设置 textMessage.value，避免覆盖用户可能的编辑
    console.log('📤 [inputBar] 语音识别完成，文字已插入到光标位置:', voiceMessage.value);
  } else {
    showToast('录音解析为空，请重新录制~');
  }
  // 清空语音消息和上次识别文字
  voiceMessage.value = '';
  lastVoiceText.value = '';
}

// 处理语音按钮点击 - 直接开始录音
const handleVoiceButtonClick = async () => {
  await startRecording();
};

// 新增方法：处理文字输入
const handleInput = () => {
  emit('update:modelValue', textMessage.value);
};

// 新增方法：处理输入法组合事件
const handleComposition = (e: CompositionEvent) => {
  const { type } = e;
  isOnComposition.value = type !== 'compositionend';
};

// 判断设备类型
const judgeDeviceType = (() => {
  const ua = window.navigator.userAgent.toLowerCase();
  const isIOS = /iphone|ipad|ipod/.test(ua);
  const isAndroid = /android/.test(ua);

  return {
    isIOS,
    isAndroid,
  };
})();

// 修复iOS虚拟键盘问题的方法
const fixIOSKeyboardIssue = () => {
  // 检查是否为iOS设备
  if (!judgeDeviceType.isIOS) return;

  // 检查微信版本和iOS版本
  const wechatInfo = window.navigator.userAgent.match(/MicroMessenger\/([\d.]+)/i);
  if (!wechatInfo) return;

  const wechatVersion = wechatInfo[1];
  const version = navigator.userAgent.match(/OS (\d+)_(\d+)_?(\d+)?/);

  if (!version) return;

  // 微信版本6.7.4+且iOS12+会出现键盘收起后视图被顶上去的问题
  if (+wechatVersion.replace(/\./g, '') >= 674 && +version[1] >= 12) {
    setTimeout(() => {
      // 将页面滚动到底部，修复键盘收起后页面位置异常的问题
      window.scrollTo(
        0,
        Math.max(document.body.clientHeight, document.documentElement.clientHeight),
      );
    }, 10);
  }
};

// 在光标位置插入文字的工具函数
const insertTextAtCursor = (newText: string) => {
  if (!textInputRef.value) return;

  const inputElement =
    textInputRef.value.$el.querySelector('textarea') ||
    textInputRef.value.$el.querySelector('input');
  if (!inputElement) return;

  const start = (inputElement.selectionStart as number) || 0;
  const end = (inputElement.selectionEnd as number) || 0;
  const currentValue = textMessage.value;

  // 在光标位置插入新文字
  const newValue = currentValue.slice(0, start) + newText + currentValue.slice(end);
  textMessage.value = newValue;

  // 更新光标位置到插入文字的末尾
  const newCursorPosition = start + newText.length;

  // 使用 nextTick 确保 DOM 更新后再设置光标位置
  void nextTick(() => {
    inputElement.setSelectionRange(newCursorPosition, newCursorPosition);
    inputElement.focus();
  });
};

// 发送文字消息
const handleSend = () => {
  if (textMessage.value === '' || props.isLoading || isOnComposition.value) {
    return;
  }
  // 发送消息
  emit('send', textMessage.value);
  textMessage.value = '';
};



// 监听键盘事件
const setupKeyboardListeners = () => {
  if (judgeDeviceType.isIOS) {
    // iOS设备监听输入框的focus和blur事件
    const inputElement =
      textInputRef.value?.$el?.querySelector('textarea') ||
      textInputRef.value?.$el?.querySelector('input');

    if (inputElement) {
      // 键盘弹起
      inputElement.addEventListener(
        'focus',
        () => {
          console.log('🎹 [inputBar] iOS键盘弹起');
        },
        false,
      );

      // 键盘收起
      inputElement.addEventListener(
        'blur',
        () => {
          console.log('🎹 [inputBar] iOS键盘收起，执行修复');
          // 延迟执行修复，确保键盘完全收起
          setTimeout(() => {
            fixIOSKeyboardIssue();
          }, 300);
        },
        false,
      );
    }
  }

  if (judgeDeviceType.isAndroid) {
    // Android设备监听窗口resize事件
    const originHeight = document.documentElement.clientHeight || document.body.clientHeight;

    window.addEventListener(
      'resize',
      () => {
        const resizeHeight = document.documentElement.clientHeight || document.body.clientHeight;
        if (originHeight < resizeHeight) {
          console.log('🎹 [inputBar] Android键盘收起');
        } else {
          console.log('🎹 [inputBar] Android键盘弹起');
        }
      },
      false,
    );
  }
};

onMounted(() => {
  console.log('🎬 [inputBar] 组件挂载，开始初始化');

  // 加载用户头像
  void loadUserAvatar();

  // 延迟设置键盘监听，确保DOM完全渲染
  void nextTick(() => {
    setupKeyboardListeners();
  });
});

// 组件卸载时释放麦克风资源
onBeforeUnmount(() => {
  console.log('🧹 [inputBar] 组件卸载，释放麦克风资源');
  if (isRecording.value) {
    // 如果正在录音，先停止录音
    if (recorder) {
      recorder.stop();
    }
    isRecording.value = false;
    // 通知父组件录音状态变化
    emit('recording-status', false);
  }
  // 释放麦克风资源
  releaseMicrophoneResources();
});
</script>

<style scoped lang="scss">
.input-bar {
  width: 100%;
  min-height: auto;
  border: 2px solid var(--border-accent);
  border-bottom: none;
  border-radius: 20px 20px 0px 0px;
  padding: 16px 23px;
  box-sizing: border-box;
  padding: 16px;
  // padding-bottom: calc(constant(safe-area-inset-bottom) + 16px);
  // padding-bottom: calc(env(safe-area-inset-bottom) + 16px);
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  transition: all 0.3s ease;
  position: relative;
}

// 输入模式样式
.keyboard-wrapper {
  display: flex;
  align-items: center; // 改为中心对齐
  width: 100%;

  .input-box {
    flex: 1;
    display: flex;
    align-items: stretch;
    margin-right: 8px; // 进一步减少右边距，为用户头像留出更多空间

    .input-content-wrapper {
      width: 100%;
      position: relative;
      display: flex;
      align-items: stretch;

      .voice-toggle-inner {
        position: absolute;
        right: 20px; // 增加右边距，为更大的按钮留出空间
        top: 50%;
        transform: translateY(-50%);
        width: 60px; // 增大按钮尺寸，更容易点击
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-radius: 50%;
        background: var(--bg-glass-light);
        border: 3px solid var(--border-accent); // 统一边框粗细为3px
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;
        z-index: 10;
        box-shadow: var(--shadow-medium); // 添加统一阴影

        &.breathing {
          animation: breathing 2s ease-in-out infinite;
        }

        .iconfont {
          font-size: 40px; // 增大语音按钮图标，更容易识别和点击
          color: var(--text-primary);
        }
      }
    }

    .input-content {
      width: 100%;
      max-height: 192px;
      min-height: 70px;
      padding: 14px 90px 14px 24px !important; // 右侧留出更多空间给更大的麦克风按钮
      box-sizing: border-box;
      border-radius: var(--border-radius-xl);
      background: var(--bg-glass-light);
      border: 2px solid var(--border-accent);
      backdrop-filter: blur(20px);
      transition: all 0.3s ease;
      box-shadow: var(--shadow-medium);

      &:focus-within {
        border-color: var(--accent-color);
        box-shadow:
          0 0 0 3px var(--accent-color-light),
          var(--shadow-strong);
        transform: translateY(-2px);
      }

      :deep(.van-field__control) {
        color: #000000; /* 输入文字改为黑色 */
        font-size: calc(var(--font-size-2xl) + 6px) !important;
        line-height: 1.4;
        max-height: 165px;
        font-weight: 500;
        padding-right: 100px !important; // 为更大的语音按钮留出更多空间
      }

      :deep(.van-field__control::placeholder) {
        color: #888888; /* placeholder改为灰色 */
        font-size: calc(var(--font-size-2xl) + 6px) !important;
        font-weight: 400;
      }
    }
  }

  .user-avatar-container {
    display: flex;
    align-items: center;
    margin-left: 16px; // 位于发送按钮右侧，增加一些左间距

    .user-avatar {
      width: 80px; // 增大用户头像，更容易点击
      height: 80px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 32px; // 增大字体大小
      font-weight: bold;
      border: 3px solid var(--border-glass); // 统一边框粗细为3px
      backdrop-filter: blur(20px);
      transition: all 0.3s ease;
      box-shadow: var(--shadow-medium);
      overflow: hidden; // 确保图片不会溢出圆形边界

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
      }


    }
  }

  .send-btn {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    .send-icon {
      width: 60px;
      height: 60px;
      min-width: 60px;
      min-height: 60px;
      padding-right: 10px;
    }
  }
}

.voice-text-display {
  height: 100px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  justify-content: center;

  .voice-placeholder {
    color: #000000;
    font-size: 28px;
    font-weight: 500;
    opacity: 0.7;
    text-align: center;
  }

  .voice-message-text {
    color: #000000;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5;
    text-align: center;
    max-height: 110px; // 相应增加最大高度
    overflow-y: auto;
  }
}

.button-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 60px;
  position: relative;

  .voice-wave {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    height: 40px;
    margin: 0 15px;

    .wave-line {
      width: 3px;
      height: 15px;
      border-radius: 3px;

      &:nth-child(5n + 1) {
        background-color: rgba(255, 255, 255, 0.9);
        animation: wave1 0.5s infinite ease-in-out alternate;
      }
      &:nth-child(5n + 2) {
        background-color: rgba(255, 255, 255, 0.7);
        animation: wave2 0.6s infinite ease-in-out alternate;
      }
      &:nth-child(5n + 3) {
        background-color: rgba(255, 255, 255, 0.8);
        animation: wave3 0.5s infinite ease-in-out alternate;
      }
      &:nth-child(5n + 4) {
        background-color: rgba(255, 255, 255, 0.6);
        animation: wave4 0.7s infinite ease-in-out alternate;
      }
      &:nth-child(5n) {
        background-color: rgba(255, 255, 255, 0.95);
        animation: wave5 0.55s infinite ease-in-out alternate;
      }

      &:nth-child(1) {
        animation-delay: 0s;
      }
      &:nth-child(2) {
        animation-delay: 0.05s;
      }
      &:nth-child(3) {
        animation-delay: 0.1s;
      }
      &:nth-child(4) {
        animation-delay: 0.15s;
      }
      &:nth-child(5) {
        animation-delay: 0.2s;
      }
      &:nth-child(6) {
        animation-delay: 0.25s;
      }
      &:nth-child(7) {
        animation-delay: 0.3s;
      }
      &:nth-child(8) {
        animation-delay: 0.35s;
      }
      &:nth-child(9) {
        animation-delay: 0.4s;
      }
      &:nth-child(10) {
        animation-delay: 0.45s;
      }
    }
  }
}

@keyframes breathing {
  0% {
    transform: translateY(-50%) scale(1);
    box-shadow: 0 0 0 0 var(--primary-color);
  }
  50% {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 0 0 8px var(--primary-color-medium);
  }
  100% {
    transform: translateY(-50%) scale(1);
    box-shadow: 0 0 0 0 var(--primary-color);
  }
}

// 移动端适配
@media (max-width: 768px) {
  .input-bar {
    .keyboard-wrapper {
      .user-avatar-container {
        margin-left: 12px; // 移动端也增加一些左间距

        .user-avatar {
          width: 64px; // 移动端也增大一些，更容易点击
          height: 64px;
          font-size: 24px;
          border: 2px solid var(--border-glass); // 移动端边框稍细一些
        }
      }

      .send-btn {
        width: 64px;
        height: 64px;
        border: 2px solid var(--primary-color);

        .send-icon {
          width: 48px;
          height: 48px;
          min-width: 48px;
          padding-right: 6px;
        }
      }

      .input-box {
        .input-content-wrapper {
          .voice-toggle-inner {
            width: 64px; // 移动端语音按钮也增大
            height: 64px;
            right: 16px; // 移动端减少右边距
            border: 2px solid var(--border-accent); // 移动端边框稍细一些
            box-shadow: var(--shadow-medium); // 统一阴影

            .iconfont {
              font-size: 28px; // 增大移动端语音图标
            }
          }
        }

        :deep(.van-field__control) {
          padding-right: 80px !important; // 为更大的语音按钮留出空间
        }
      }
    }
  }
}
</style>
