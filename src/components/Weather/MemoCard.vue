<template>
  <div class="memo-card">
    <!-- 标题区域 -->
    <div class="memo-header">
      <div class="memo-title-section">
        <h3 class="memo-title">备忘录</h3>
        <span class="memo-subtitle">懂你，服务会更好</span>
      </div>
      <div class="memo-actions">
        <button class="action-btn add-event-btn" @click="handleAddEvent">
          <PlusIcon :size="20" color="var(--primary-color)" />
        </button>
        <button class="action-btn voice-play-btn" @click="handleVoicePlay">
          <TrumpetIcon :size="20" color="var(--primary-color)" />
        </button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="memo-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-text">正在加载备忘录...</div>
      </div>

      <!-- 标签列表 -->
      <div v-else-if="memories.length > 0" class="memo-tags-container">
        <div ref="tagsScrollRef" class="memo-tags-scroll">
          <div
            v-for="memory in memories"
            :key="memory.event_id"
            class="memo-tag"
            @click="handleTagClick(memory)"
          >
            {{ memory.description_text }}
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <div class="empty-text">暂无备忘录内容</div>
        <div class="empty-hint">点击"添加事件"开始记录</div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { getPersonMemories, type IEvent } from '@/apis/memory';
import { showFailToast } from 'vant';
import { useAudioQueue } from '@/pages/Chat/useAudioPlayer';
import TrumpetIcon from '@/assets/icons/TrumpetIcon.vue';
import PlusIcon from '@/assets/icons/PlusIcon.vue';

// Props定义
interface IProps {
  userId: string;
  personId: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  addEvent: [];
  editEvent: [event: IEvent];
}>();

// 响应式数据
const loading = ref(true);
const memories = ref<IEvent[]>([]);
const tagsScrollRef = ref<HTMLElement>();

// TTS相关
const { play, stop, isCurrentAudioPlaying } = useAudioQueue();
const ttsId = 'memo-card-tts';

// 加载备忘录数据
const loadMemories = async () => {
  try {
    loading.value = true;
    console.log('🔄 [MemoCard] 开始加载备忘录数据:', {
      userId: props.userId,
      personId: props.personId,
    });

    const response = await getPersonMemories({
      user_id: props.userId,
      person_id: props.personId,
    });

    console.log('📡 [MemoCard] 备忘录数据响应:', response);

    if (response && response.result === 'success') {
      memories.value = response.events || [];
      console.log('✅ [MemoCard] 备忘录数据加载成功:', memories.value.length, '条记录');
    } else {
      console.warn('⚠️ [MemoCard] 备忘录数据加载失败:', response);
      memories.value = [];
    }
  } catch (error) {
    console.error('❌ [MemoCard] 备忘录数据加载失败:', error);
    showFailToast('加载备忘录失败');
    memories.value = [];
  } finally {
    loading.value = false;
  }
};

// 处理添加事件
const handleAddEvent = () => {
  console.log('➕ [MemoCard] 添加事件按钮点击');
  emit('addEvent');
};

// 处理语音播放
const handleVoicePlay = () => {
  if (isCurrentAudioPlaying(ttsId)) {
    stop();
  } else {
    // 构建朗读内容
    const ttsContent = memories.value.length > 0 
      ? `您有${memories.value.length}条备忘录：${memories.value.slice(0, 3).map(m => m.description_text).join('，')}`
      : '暂无备忘录内容，点击添加事件开始记录';

    if (ttsContent.trim()) {
      play({
        id: ttsId,
        text: ttsContent,
        type: 'manualPlay',
      });
    }
  }
};

// 处理标签点击
const handleTagClick = (memory: IEvent) => {
  console.log('🏷️ [MemoCard] 标签点击:', memory);
  emit('editEvent', memory);
};

// 公开的刷新方法，供父组件调用
const refreshMemories = () => {
  console.log('🔄 [MemoCard] 外部调用刷新备忘录数据');
  void loadMemories();
};

// 暴露给父组件的方法
defineExpose({
  refreshMemories,
});

// 监听自定义事件，刷新数据
const handleAddEventSuccessEvent = () => {
  console.log('🔄 [MemoCard] 收到添加事件成功事件，延迟1100ms后刷新数据');
  setTimeout(() => {
    console.log('🔄 [MemoCard] 延迟刷新备忘录数据（添加事件成功事件）');
    void loadMemories();
  }, 1100);
};

const handleEditEventSuccessEvent = () => {
  console.log('🔄 [MemoCard] 收到编辑事件成功事件，延迟1100ms后刷新数据');
  setTimeout(() => {
    console.log('🔄 [MemoCard] 延迟刷新备忘录数据（编辑事件成功事件）');
    void loadMemories();
  }, 1100);
};

// 组件挂载时加载数据并监听自定义事件
onMounted(() => {
  void loadMemories();

  // 监听自定义事件
  window.addEventListener('addeventsuccess', handleAddEventSuccessEvent);
  window.addEventListener('editeventsuccess', handleEditEventSuccessEvent);
});

// 组件卸载时清理事件监听
onBeforeUnmount(() => {
  window.removeEventListener('addeventsuccess', handleAddEventSuccessEvent);
  window.removeEventListener('editeventsuccess', handleEditEventSuccessEvent);
});
</script>

<style scoped lang="scss">
.memo-card {
  height: 200px;
  width: 100%;
  background: var(--bg-glass-light);
  border-radius: 20px;
  box-shadow: var(--shadow-soft);
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin: 0 auto;
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-glass);

  // 标题区域
  .memo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;

    .memo-title-section {
      display: flex;
      align-items: center;
      gap: 12px;

      .memo-title {
        font-size: 30px; // 增加4px
        font-weight: 600;
        color: var(--page-text-primary);
        margin: 0;
      }

      .memo-subtitle {
        font-size: 24px; // 增加4px
        color: var(--page-text-primary);
        opacity: 0.9;
      }
    }

    .memo-actions {
      display: flex;
      align-items: center;
      gap: 8px;
      .action-btn {
        padding: 8px 8px;
        border: none;
        border-radius: 50%;
        background: var(--primary-color-light);
        color: var(--page-text-primary);
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 4px;

        &:hover {
          background: var(--primary-color-medium);
          transform: translateY(-1px);
        }

        &.voice-play-btn {
          padding: 8px;
          min-width: 36px;
          justify-content: center;
        }
      }
    }
  }

  // 内容区域
  .memo-content {
    flex: 1;
    overflow-x: auto;
    display: flex;
    flex-direction: column;

    // 加载状态
    .loading-state {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;

      .loading-text {
        font-size: 16px;
        color: var(--page-text-secondary);
      }
    }

    // 标签容器
    .memo-tags-container {
      height: 100%;
      overflow: hidden;

      .memo-tags-scroll {
        display: flex;
        gap: 12px;
        overflow-x: auto;
        overflow-y: hidden;
        height: 100%;
        align-items: flex-start;
        padding: 8px 0;

        // 隐藏滚动条
        &::-webkit-scrollbar {
          height: 4px;
        }

        &::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb {
          background: var(--primary-color-medium);
          border-radius: 2px;
        }

        .memo-tag {
          flex-shrink: 0;
          padding: 8px 16px;
          background: rgba(255, 255, 0, 0.3);
          border: 1px solid rgba(255, 255, 0, 0.3);
          border-radius: 16px;
          color: var(--page-text-primary);
          font-size: 24px;
          cursor: pointer;
          transition: all 0.3s ease;
          max-width: 200px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    // 空状态
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      gap: 8px;

      .empty-text {
        font-size: 16px;
        color: var(--page-text-secondary);
      }

      .empty-hint {
        font-size: 14px;
        color: var(--page-text-tertiary);
        opacity: 0.7;
      }
    }
  }
}
</style>
