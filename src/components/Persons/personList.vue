<template>
  <div class="person-list-container">
    <!-- 人物列表 -->
    <div class="person-content">
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载中...</div>
      </div>

      <div v-else-if="displayPersons.length === 0" class="empty-container">
        <div class="empty-text">
          {{ props.isSearchMode ? '未找到相关人员' : '暂无人际关系记录' }}
        </div>
      </div>

      <div v-else class="person-items">
        <div
          v-for="person in displayPersons"
          :key="person.person_id"
          class="person-item"
          @click="handleShowPersonDetail(person)"
        >
          <!-- 右上角编辑按钮 -->
          <button class="edit-btn-corner" title="编辑" @click.stop="handleEditPerson(person)">
            <EditIcon class="edit-icon" />
          </button>

          <!-- 左侧头像 -->
          <div class="person-avatar-section">
            <img
              :src="getAvatarUrl(person.avatar, person.person_id)"
              :alt="person.canonical_name"
              class="person-avatar"
            />
          </div>

          <div class="person-info">
            <div class="person-name-row">
              <span class="person-name" :class="{ 'use-primary-color': shouldUsePrimaryColor }">{{ person.canonical_name }}</span>
              <span v-if="getKeyAttribute(person, '关系')" class="person-relation">{{
                getKeyAttribute(person, '关系')
              }}</span>
            </div>
            <div v-if="person.profile_summary" class="person-summary" :class="{ 'use-primary-color': shouldUsePrimaryColor }">
              {{ person.profile_summary }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加人员对话框 -->
    <PersonAddDialog
      v-if="showAddPersonDialog"
      :user-id="props.userId"
      @close="closeAddPersonDialog"
      @success="handleAddPersonSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { getPersons, type IPersonData } from '@/apis/relation';
import { showFailToast } from 'vant';
import { getAvatarUrl } from '@/utils/avatarUtils';

import EditIcon from '@/assets/icons/EditIcon.vue';
import PersonAddDialog from './PersonAddDialog.vue';

// Props定义
interface IProps {
  userId: string;
  searchResults?: IPersonData[];
  isSearchMode?: boolean;
}

const props = withDefaults(defineProps<IProps>(), {
  searchResults: () => [],
  isSearchMode: false,
});

// Emits定义
const emit = defineEmits<{
  close: [];
  refresh: [];
  showPersonDetail: [personId: string, personName: string];
  editPerson: [person: IPersonData];
}>();

// 响应式数据
const loading = ref(true);
const persons = ref<IPersonData[]>([]);
const showAddPersonDialog = ref(false);

// 搜索相关数据（已移除，现在使用外部搜索）
// const showSearchInput = ref(true);
// const searchQuery = ref('');
// const isSearching = ref(false);
// isSearchMode 现在来自props

// 安全地获取key_attributes中的属性值
const getKeyAttribute = (person: IPersonData, key: string): string => {
  if (!person.key_attributes) return '';

  if (typeof person.key_attributes === 'string') {
    try {
      const parsed = JSON.parse(person.key_attributes);
      return parsed[key] || '';
    } catch {
      return '';
    }
  }

  // 由于key_attributes已经被扁平化，直接获取值
  const value = person.key_attributes[key];
  return value && String(value).trim() ? String(value) : '';
};

// 计算显示的人员数据：如果是搜索模式则显示搜索结果，否则显示原始数据
const displayPersons = computed(() => {
  return props.isSearchMode ? props.searchResults : persons.value;
});

// 判断是否应该使用主题色（当主题返回白色时）
const shouldUsePrimaryColor = computed(() => {
  // 获取CSS变量 --on-primary-text 的值
  const onPrimaryText = getComputedStyle(document.documentElement).getPropertyValue('--on-primary-text').trim();
  return onPrimaryText === '#ffffff';
});

// 获取人员数据
const loadPersons = async () => {
  try {
    loading.value = true;
    console.log('🔄 [personList.vue] 开始获取人员数据...', {
      userId: props.userId,
    });

    const response = await getPersons({
      userId: props.userId,
      limit: 100,
      offset: 0,
    });

    console.log('📡 [personList.vue] 人员数据响应:', response);

    if (response && response.result === 'success' && response.persons) {
      // 过滤掉用户自己
      persons.value = response.persons.filter((person) => !person.is_user);
      console.log('✅ [personList.vue] 人员数据加载成功，共', persons.value.length, '个人员');
    } else {
      console.warn('⚠️ [personList.vue] 人员数据格式异常:', response);
      persons.value = [];
    }
  } catch (error) {
    console.error('❌ [personList.vue] 获取人员数据失败:', error);
    showFailToast('获取人员数据失败');
    persons.value = [];
  } finally {
    loading.value = false;
  }
};

// 关闭添加人员对话框
const closeAddPersonDialog = () => {
  showAddPersonDialog.value = false;
};

// 处理添加人员成功
const handleAddPersonSuccess = async () => {
  // 刷新人员列表
  await loadPersons();
  // 通知父组件刷新关系图
  emit('refresh');
};

// 处理显示人员详情
const handleShowPersonDetail = (person: IPersonData) => {
  // 通知父组件显示人员详情
  emit('showPersonDetail', person.person_id, person.canonical_name);
};

// 处理编辑人员
const handleEditPerson = (person: IPersonData) => {
  // 通知父组件显示编辑弹窗
  emit('editPerson', person);
};

// 搜索相关方法已移除，现在使用外部搜索

// 组件挂载时加载数据
onMounted(() => {
  void loadPersons();
});

// 暴露方法给父组件调用
defineExpose({
  loadPersons,
});
</script>

<style lang="scss" scoped>
.person-list-container {
  background: transparent;
  border: none;
  border-radius: 16px;
  padding: 0px 30px;
  margin-bottom: 0; // 移除底部margin，避免与reminder-container重合
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.person-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow-y: auto;
  min-height: 0; // 确保可以收缩

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: var(--bg-glass-light);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--border-glass);
    border-radius: 2px;

    &:hover {
      background: var(--border-accent);
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 50px 0;

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-glass);
    border-top: 3px solid var(--text-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  .loading-text {
    color: var(--text-primary);
    font-size: 36px; // 增大字体，原来是32px
    font-weight: 600;
    line-height: 1.4;
  }
}

.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 50px 0;
  text-align: center;

  .empty-text {
    color: var(--text-primary);
    font-size: 36px; // 增大字体，原来是32px
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 12px;
  }

  .add-person-hint {
    color: var(--text-tertiary);
    font-size: 26px; // 增大字体，原来是22px
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.person-items {
  display: flex;
  flex-direction: column;
  padding-bottom: 10px;
  gap: 28px;

  .person-item {
    position: relative; // 添加相对定位，为绝对定位的删除按钮提供定位基准
    display: flex;
    align-items: center; // 改为center让子元素垂直居中
    gap: 20px; // 添加间距
    padding: 20px;
    border: none;
    border-radius: 16px;
    background: var(--primary-color-light);
    backdrop-filter: blur(10px);
    border-left: 4px solid var(--accent-color);
    box-shadow: var(--shadow-accent);
    min-height: 90px; // 设置最小高度
    cursor: pointer; // 添加点击光标
    transition: all 0.3s ease; // 添加过渡效果



    &:last-child {
      border-left: 4px solid var(--accent-color);
    }

    // 头像区域样式
    .person-avatar-section {
      flex-shrink: 0; // 防止头像被压缩
      width: 80px;
      height: 80px;

      .person-avatar {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid var(--accent-color);
        box-shadow: var(--shadow-accent);
        transition: all 0.3s ease;
      }

      .person-avatar-placeholder {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: var(--primary-color-strong);
        border: 2px solid var(--accent-color);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-primary);
        font-size: 24px;
        font-weight: 600;
        text-transform: uppercase;
        box-shadow: var(--shadow-accent);
        transition: all 0.3s ease;
      }
    }

    .person-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 12px;
      min-width: 0; // 允许收缩，防止文本溢出

      .person-name-row {
        display: flex;
        align-items: center;
        gap: 12px;

        .person-name {
          color: var(--page-text-primary);
          font-size: 30px; // 增大字体，原来是26px
          font-weight: 600;
          line-height: 1.4;
        }

        // 当主题返回白色时，使用主题色
        .person-name.use-primary-color {
          color: #000000;
        }

        .person-relation {
          color: var(--page-text-secondary);
          font-size: 26px; // 增大字体，原来是22px
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }

      .person-summary {
        color: var(--page-text-secondary);
        font-size: 26px; // 增大字体，原来是22px
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        line-height: 1.4;
        user-select: text;

        // 默认只显示一行
        display: -webkit-box;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      // 当主题返回白色时，使用主题色
      .person-summary.use-primary-color {
        color: #000000;
      }
    }

    // 右上角编辑按钮
    .edit-btn-corner {
      position: absolute;
      top: 10px;
      right: 10px;
      background: transparent;
      border: none;
      border-radius: 12px;
      width: 44px;
      height: 44px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;
      z-index: 10; // 确保在最上层

      .edit-icon {
        min-width: 50px;
        min-height: 50px;
      }
    }

    .person-actions {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-right: 20px;
      justify-content: center; // 居中对齐编辑按钮

      .edit-btn {
        background: transparent;
        border: 3px solid var(--primary-color);
        border-radius: 12px;
        width: 60px;
        height: 60px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
        flex-shrink: 0;

        &:hover {
          background: var(--primary-color-light);
          transform: translateY(-2px);
          box-shadow: var(--shadow-accent);
        }

        .edit-icon {
          width: 28px;
          height: 28px;
        }
      }
    }
  }
}

// 对话框样式
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

.dialog-container {
  background: rgba(1, 28, 32, 0.6);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 500px;
  color: white;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;

  &.edit-dialog {
    max-width: 600px;
    max-height: 1200px;
    overflow-y: auto;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
  padding-bottom: 18px;
  position: relative;

  .dialog-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 40px; // 增加8px (原来32px)
    font-weight: 600;
  }

  .dialog-close {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }

    .close-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) invert(1);
    }
  }
}

.dialog-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 32px;

  &.edit-content {
    max-height: 60vh;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }
  }

  .input-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
    border: 2px solid rgba(0, 188, 212, 0.3);
    border-radius: 16px;
    padding: 20px;
    background: var(--bg-glass-light);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    &.avatar-name-group {
      margin-bottom: 30px;

      .labels-row {
        display: flex;
        gap: 24px;
        margin-bottom: 12px;

        .avatar-label {
          width: 120px;
          text-align: left;
          flex-shrink: 0;
          padding-left: 18px;
        }

        .name-label {
          flex: 1;
        }
      }

      .content-row {
        display: flex;
        gap: 24px;
        align-items: flex-end;

        .avatar-section {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          flex-shrink: 0;
          width: 120px;

          .person-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid rgba(139, 69, 19, 0.8);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

            &.clickable-avatar {
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                border-color: #00ffff;
                box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
                transform: scale(1.05);
              }
            }
          }
        }

        .name-section {
          flex: 1;

          .input-field {
            width: 100%;
          }
        }
      }
    }

    .input-label {
      color: white;
      font-size: 30px; // 增加8px (原来22px)
      font-weight: 600;
      margin-bottom: 8px;
    }

    .input-field {
      width: 100%;
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(0, 188, 212, 0.3);
      border-radius: 20px;
      padding: 18px 22px;
      color: rgba(255, 255, 255, 0.9);
      font-size: 30px; // 增加8px (原来22px)
      line-height: 1.6;
      box-sizing: border-box;
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        outline: none;
        border-color: #00bcd4;
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(0, 188, 212, 0.2);
      }
    }

    .textarea-field {
      width: 100%;
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(0, 188, 212, 0.3);
      border-radius: 20px;
      padding: 18px 22px;
      color: rgba(255, 255, 255, 0.9);
      font-size: 30px; // 增加8px (原来22px)
      line-height: 1.6;
      box-sizing: border-box;
      backdrop-filter: blur(10px);
      resize: vertical;
      min-height: 100px;
      font-family: inherit;
      transition: all 0.3s ease;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        outline: none;
        border-color: #00bcd4;
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(0, 188, 212, 0.2);
      }
    }

    // 关键属性容器样式
    .key-attributes-container {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .attribute-item,
      .add-attribute-container {
        display: flex;
        gap: 12px;
        align-items: center;
        width: 100%;
        box-sizing: border-box;
      }

      // 为新增属性容器提供更紧凑的布局
      .add-attribute-container {
        .attribute-value {
          flex: 1;
          min-width: 0; // 允许收缩
          max-width: calc(100% - 200px); // 为关闭按钮预留空间
        }
      }

      .attribute-item,
      .add-attribute-container {
        .attribute-key,
        .attribute-value {
          background: rgba(255, 255, 255, 0.05);
          border: 2px solid rgba(0, 188, 212, 0.3);
          border-radius: 12px;
          padding: 16px;
          color: rgba(255, 255, 255, 0.9);
          font-size: 28px; // 增加8px (原来22px)
          line-height: 1.6;
          box-sizing: border-box;
          backdrop-filter: blur(10px);
          transition: all 0.2s ease;

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }

          &:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.1);
            box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.3);
          }

          &:read-only {
            background: rgba(255, 255, 255, 0.02);
            color: rgba(255, 255, 255, 0.6);
          }
        }

        .attribute-key {
          flex: 0 0 150px; // 固定宽度，防止过宽
          min-width: 150px;
          max-width: 150px;
        }

        .attribute-value {
          flex: 1;
          min-width: 0; // 允许收缩
        }

        .remove-attribute-btn {
          background: transparent;
          border: none;
          border-radius: 50%;
          width: 36px;
          height: 36px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          color: rgba(255, 255, 255, 0.8);
          transition: all 0.3s ease;
          font-size: 28px; // 增加8px (原来20px)
          flex-shrink: 0; // 防止被压缩
          margin-left: 8px; // 增加左边距，确保与输入框有间隔

          &:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            transform: scale(1.1);
          }
        }
      }

      .add-attribute-btn-container {
        margin-top: 16px;

        .add-attribute-btn {
          width: 100%;
          background: transparent;
          color: #00bcd4;
          border: 2px solid #00bcd4;
          border-radius: 20px;
          padding: 16px 16px;
          font-size: 36px; // 增加8px (原来28px)
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 60px;

          &:hover {
            background: rgba(0, 188, 212, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
          }
        }
      }
    }
  }

  .delete-warning {
    color: #ffffff;
    font-size: 40px; // 增加8px (原来32px)
    font-weight: 600;
    line-height: 1.4;
    margin: 0 0 12px 0;
    text-align: center;

    strong {
      color: #ef4444;
    }
  }

  .delete-hint {
    color: rgba(255, 255, 255, 0.7);
    font-size: 32px; // 增加8px (原来24px)
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center;
  }
}

.dialog-footer {
  display: flex;
  gap: 20px;
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);

  .cancel-btn,
  .confirm-btn,
  .delete-confirm-btn {
    flex: 1;
    padding: 16px 16px;
    border-radius: 20px;
    font-size: 36px; // 增加8px (原来28px)
    font-weight: 600;
    cursor: pointer;
    border: 2px solid;
    background: transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
    width: 200px;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .cancel-btn {
    color: rgba(255, 255, 255, 0.8);
    border-color: rgba(255, 255, 255, 0.8);

    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
    }
  }

  .confirm-btn {
    color: #00bcd4;
    border-color: #00bcd4;

    &:hover:not(:disabled) {
      background: rgba(0, 188, 212, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
    }
  }

  .delete-confirm-btn {
    color: #ef4444;
    border-color: #ef4444;

    &:hover:not(:disabled) {
      background: rgba(239, 68, 68, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    }
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
