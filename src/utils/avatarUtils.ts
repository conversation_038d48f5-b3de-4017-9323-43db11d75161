// 导入默认头像
import defaultAvatar from '@/assets/icon/user_avatar.png';

// 新的默认头像URL列表 - 16个头像按用户提供的顺序
const defaultAvatarUrls = [
  'https://msstest.sankuai.com/aigc-public-resources/avatar/19ac8317-fee9-4688-a0b4-724dfc77cfe5_1756114725844.png',
  'https://msstest.sankuai.com/aigc-public-resources/avatar/0038c437-6172-44dc-a991-938a15d3d273_1756114761849.png',
  'https://msstest.sankuai.com/aigc-public-resources/avatar/5f2e98f2-742e-4f0e-abf2-c288c068d4fa_1756114791399.png',
  'https://msstest.sankuai.com/aigc-public-resources/avatar/bb7021ab-17ab-4eca-a029-4d902cd7ee45_1756114829467.png',
  'https://msstest.sankuai.com/aigc-public-resources/avatar/9fbd2aa7-5dc8-43bf-90e2-8bdc8872454d_1756114853992.png',
  'https://msstest.sankuai.com/aigc-public-resources/avatar/c5618d7f-fe59-4e1c-9375-4c2390809e52_1756114872007.png',
  'https://msstest.sankuai.com/aigc-public-resources/avatar/d17dbbbe-b437-472c-a6da-caa86d4a5776_1756114887292.png',
  'https://msstest.sankuai.com/aigc-public-resources/avatar/9e548a34-ef47-4880-a8f3-5e3861246faf_1756114901722.png',
  'https://msstest.sankuai.com/aigc-public-resources/avatar/1d71e2cf-b2dd-4930-be62-8f881244aab1_1756114918192.png',
  'https://msstest.sankuai.com/aigc-public-resources/avatar/184b3d51-7ae0-43f8-a5c0-da8c706c12ad_1756114934749.png',
  'https://msstest.sankuai.com/aigc-public-resources/avatar/caae3938-8a95-4e39-8d70-58aec92a2604_1756114953348.png',
  'https://msstest.sankuai.com/aigc-public-resources/avatar/90bf4447-295c-4115-b466-cbc52379bea3_1756114970067.png',
  'https://msstest.sankuai.com/aigc-public-resources/avatar/49b97be6-8336-4901-a265-96c7a6e3fc61_1756114988508.png',
  'https://msstest.sankuai.com/aigc-public-resources/avatar/3f39d43c-2486-4f61-b0b0-e526e0dd618e_1756115003448.png',
  'https://msstest.sankuai.com/aigc-public-resources/avatar/d0d4ff29-f41a-4190-a1d6-95d175fd8827_1756115020368.png',
  'https://msstest.sankuai.com/aigc-public-resources/avatar/58d584e0-d3d8-4611-9b1b-19551ea49ff7_1756115035650.png',
];

// 默认头像映射 - 支持16个头像
const defaultAvatarMap: Record<string, string> = {
  default_avatar_1: defaultAvatarUrls[0],
  default_avatar_2: defaultAvatarUrls[1],
  default_avatar_3: defaultAvatarUrls[2],
  default_avatar_4: defaultAvatarUrls[3],
  default_avatar_5: defaultAvatarUrls[4],
  default_avatar_6: defaultAvatarUrls[5],
  default_avatar_7: defaultAvatarUrls[6],
  default_avatar_8: defaultAvatarUrls[7],
  default_avatar_9: defaultAvatarUrls[8],
  default_avatar_10: defaultAvatarUrls[9],
  default_avatar_11: defaultAvatarUrls[10],
  default_avatar_12: defaultAvatarUrls[11],
  default_avatar_13: defaultAvatarUrls[12],
  default_avatar_14: defaultAvatarUrls[13],
  default_avatar_15: defaultAvatarUrls[14],
  default_avatar_16: defaultAvatarUrls[15],
};

/**
 * 根据人员ID获取随机默认头像ID
 * @param personId 人员ID，用于确保同一人员总是获得相同的头像
 * @returns 默认头像ID
 */
export function getRandomDefaultAvatarId(personId: string): string {
  const avatarIds = Object.keys(defaultAvatarMap);
  // 使用人员ID的简单哈希值来确保同一人员总是获得相同的头像
  let hash = 0;
  for (let i = 0; i < personId.length; i++) {
    const char = personId.charCodeAt(i);
    hash = (hash * 31 + char) % 1000000; // 使用简单的乘法和取模运算
  }
  const index = Math.abs(hash) % avatarIds.length;
  return avatarIds[index];
}

/**
 * 根据头像标识符获取头像URL
 * @param avatarId 头像标识符，可能是默认头像ID或自定义头像URL
 * @param personId 人员ID，当avatarId为空时用于生成随机默认头像
 * @returns 头像URL
 */
export function getAvatarUrl(avatarId: string | null | undefined, personId?: string): string {
  if (!avatarId) {
    // 如果没有头像且有人员ID，返回随机默认头像
    if (personId) {
      const randomAvatarId = getRandomDefaultAvatarId(personId);
      return defaultAvatarMap[randomAvatarId];
    }
    return defaultAvatar;
  }

  // 如果是默认头像ID，返回对应的本地图片
  if (defaultAvatarMap[avatarId]) {
    return defaultAvatarMap[avatarId];
  }

  // 如果是自定义头像URL，直接返回
  if (avatarId.startsWith('http') || avatarId.startsWith('data:') || avatarId.startsWith('blob:')) {
    return avatarId;
  }

  // 其他情况返回默认头像
  return defaultAvatar;
}

/**
 * 检查是否为默认头像
 * @param avatarId 头像标识符
 * @returns 是否为默认头像
 */
export function isDefaultAvatar(avatarId: string | null | undefined): boolean {
  return !!(avatarId && defaultAvatarMap[avatarId]);
}

/**
 * 获取所有默认头像列表
 * @returns 默认头像列表
 */
export function getDefaultAvatars() {
  return [
    { id: 'default_avatar_1', src: defaultAvatarUrls[0] },
    { id: 'default_avatar_2', src: defaultAvatarUrls[1] },
    { id: 'default_avatar_3', src: defaultAvatarUrls[2] },
    { id: 'default_avatar_4', src: defaultAvatarUrls[3] },
    { id: 'default_avatar_5', src: defaultAvatarUrls[4] },
    { id: 'default_avatar_6', src: defaultAvatarUrls[5] },
    { id: 'default_avatar_7', src: defaultAvatarUrls[6] },
    { id: 'default_avatar_8', src: defaultAvatarUrls[7] },
    { id: 'default_avatar_9', src: defaultAvatarUrls[8] },
    { id: 'default_avatar_10', src: defaultAvatarUrls[9] },
    { id: 'default_avatar_11', src: defaultAvatarUrls[10] },
    { id: 'default_avatar_12', src: defaultAvatarUrls[11] },
    { id: 'default_avatar_13', src: defaultAvatarUrls[12] },
    { id: 'default_avatar_14', src: defaultAvatarUrls[13] },
    { id: 'default_avatar_15', src: defaultAvatarUrls[14] },
    { id: 'default_avatar_16', src: defaultAvatarUrls[15] },
  ];
}

/**
 * 获取分页的默认头像列表，每页8个头像
 * @returns 分页的头像列表，共2页
 */
export function getDefaultAvatarsByPages() {
  const allAvatars = getDefaultAvatars();
  const pageSize = 8;
  const pages = [];

  for (let i = 0; i < allAvatars.length; i += pageSize) {
    pages.push(allAvatars.slice(i, i + pageSize));
  }

  return pages;
}
