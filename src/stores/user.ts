import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { IUserInfo } from '@/apis/common';

export const useUserStore = defineStore('user', () => {
  const userInfo = ref<IUserInfo | null>(null);
  const userAvatar = ref<string>(''); // 添加用户头像字段

  // 更新用户头像的方法
  const updateUserAvatar = (avatarId: string) => {
    userAvatar.value = avatarId;
    console.log('✅ [userStore] 用户头像已更新:', avatarId);
  };

  return {
    userInfo,
    userAvatar,
    updateUserAvatar,
  };
});
