import fetchInstance from '@/lib/fetch';

// 知识库条目接口定义
export interface IKnowledgeItem {
  id: number;
  term: string;
  full_name?: string;
  definition: string;
  category: 'Person' | 'Business' | 'Product' | 'Analytics' | 'Organization' | 'Technical';
  source: 'experts' | 'business' | 'personal-vocabulary';
  priority: number; // 1-5，数字越大优先级越高
  aliases: string[];
  related_terms: string[];
  add_time: string;
  update_time: string;
}

// 获取知识库列表请求接口
export interface IGetKnowledgeRequest {
  limit?: number; // 每页条数，默认100
  offset?: number; // 偏移量，默认0
  category?: string; // 可选，按类别过滤
  source?: string; // 可选，按来源过滤
}

// 获取知识库列表响应接口
export interface IGetKnowledgeResponse {
  result: string; // 请求状态
  knowledge_items: IKnowledgeItem[]; // 知识条目数组
  total_count: number; // 总条目数
  limit: number; // 每页限制
  offset: number; // 偏移量
}

// 获取知识库列表
export async function getKnowledgeList(
  params: IGetKnowledgeRequest = {}
): Promise<IGetKnowledgeResponse> {
  console.log('📤 [knowledge.ts] getKnowledgeList API调用开始:', {
    url: '/humanrelation/knowledge',
    method: 'GET',
    params,
  });

  try {
    const queryParams = {
      limit: params.limit || 100,
      offset: params.offset || 0,
      ...(params.category && { category: params.category }),
      ...(params.source && { source: params.source }),
    };

    const response = await fetchInstance.fetch('/humanrelation/knowledge', {
      method: 'GET',
      params: queryParams,
    });

    console.log('📡 [knowledge.ts] 获取知识库列表API响应:', response);
    return response;
  } catch (error) {
    console.error('❌ [knowledge.ts] 获取知识库列表API调用失败:', error);
    throw error;
  }
}
